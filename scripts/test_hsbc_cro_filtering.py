#!/usr/bin/env python3
"""
Test script to validate the enhanced external search filtering logic
for the HSBC CRO Succession Plan configuration.

This script tests the specific filtering issues:
1. <PERSON> (HSBC Singapore) - should be filtered out due to location
2. <PERSON> (Handelsbanken UK) - should be filtered out due to company
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add the scripts directory to the path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

try:
    # Import the external search engine
    from python.exa_search_standalone import ExternalSearchEngine, CandidateProfile, PeopleData, PipelineData, CareerHistory
    print("Successfully imported external search components")
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_hsbc_cro_plan():
    """Create the HSBC CRO Succession Plan configuration"""
    return {
        "plan_name": "HSBC CRO Succession Plan",
        "plan_id": "hsbc-cro-test-001",
        "target_roles": ["Chief Risk Officer", "CRO"],
        "companies": ["HSBC"],
        "gender": "Not required",
        "country": ["United Kingdom"],
        "minimum_tenure": 3,
        "include_alumni": False,  # Current employees only
        "enable_early_duplicate_detection": False,  # Disable for testing
        "use_llm_filtering": False,  # Test direct validation only
        "skills": ["Risk Management", "Financial Services"],
        "qualifications": ["Bachelor's Degree"],
        "step_up_candidates": ["none"],
        "alternative_roles_titles": []
    }

def create_test_profiles():
    """Create test candidate profiles that should be filtered out"""
    
    # Profile 1: Robert Oates - HSBC Singapore (should be filtered - wrong location)
    robert_people_data = PeopleData(
        forename="Robert",
        surname="Oates",
        gender="Male",
        country="Singapore",  # Wrong location - should be UK
        city="Singapore",
        latest_role="Chief Risk and Compliance Officer",
        company_name="HSBC",
        tenure=5.0,
        career_history="Sep-2019 | Present: Chief Risk and Compliance Officer at HSBC, Singapore\nJan-2015 | Sep-2019: Senior Risk Manager at HSBC, Hong Kong",
        skills="Risk Management, Compliance, Financial Services, Banking",
        linkedinURL="https://linkedin.com/in/robert-oates-hsbc"
    )
    
    robert_pipeline_data = PipelineData(
        first_name="Robert",
        last_name="Oates",
        gender="Male",
        latest_role="Chief Risk and Compliance Officer",
        company_name="HSBC",
        country="Singapore",
        city="Singapore",
        tenure=5,
        role_match=1.0,  # Perfect role match
        skills_match=0.9,
        location_match=0.0,  # Should be 0 - wrong location
        gender_match=1.0,
        tenure_match=1.0,
        education_match=0.0,
        total_score=3.9,
        linkedinURL="https://www.linkedin.com/in/robert-oates-hsbc"
    )
    
    robert_profile = CandidateProfile(
        people_data=robert_people_data,
        pipeline_data=robert_pipeline_data,
        career_history=[
            CareerHistory(
                people_id=0,
                role="Chief Risk and Compliance Officer",
                company_name="HSBC",
                start_date="2019-09-01",
                end_date="Present",
                tenure=5.0
            ),
            CareerHistory(
                people_id=0,
                role="Senior Risk Manager",
                company_name="HSBC",
                start_date="2015-01-01",
                end_date="2019-09-01",
                tenure=4.7
            )
        ],
        skills=[],
        match_reasoning="Strong role and company match, but located in Singapore instead of required United Kingdom"
    )
    
    # Profile 2: William O'Carroll - Handelsbanken UK (should be filtered - wrong company, alumni=False)
    william_people_data = PeopleData(
        forename="William",
        surname="O'Carroll",
        gender="Male",
        country="United Kingdom",  # Correct location
        city="London",
        latest_role="Chief Risk Officer",
        company_name="Handelsbanken",  # Wrong company - not HSBC
        tenure=4.0,
        career_history="Mar-2021 | Present: Chief Risk Officer at Handelsbanken, London\nJun-2017 | Mar-2021: Senior Risk Director at HSBC, London\nJan-2013 | Jun-2017: Risk Manager at HSBC, London",
        skills="Risk Management, Banking, Financial Services, Regulatory Compliance",
        linkedinURL="https://linkedin.com/in/william-ocarroll-risk"
    )
    
    william_pipeline_data = PipelineData(
        first_name="William",
        last_name="O'Carroll",
        gender="Male",
        latest_role="Chief Risk Officer",
        company_name="Handelsbanken",
        country="United Kingdom",
        city="London",
        tenure=4,
        role_match=1.0,  # Perfect role match
        skills_match=0.95,
        location_match=1.0,  # Correct location
        gender_match=1.0,
        tenure_match=1.0,
        education_match=0.0,
        total_score=4.95,
        linkedinURL="https://www.linkedin.com/in/william-ocarroll-risk"
    )
    
    william_profile = CandidateProfile(
        people_data=william_people_data,
        pipeline_data=william_pipeline_data,
        career_history=[
            CareerHistory(
                people_id=0,
                role="Chief Risk Officer",
                company_name="Handelsbanken",
                start_date="2021-03-01",
                end_date="Present",
                tenure=3.0
            ),
            CareerHistory(
                people_id=0,
                role="Senior Risk Director",
                company_name="HSBC",
                start_date="2017-06-01",
                end_date="2021-03-01",
                tenure=3.7
            ),
            CareerHistory(
                people_id=0,
                role="Risk Manager",
                company_name="HSBC",
                start_date="2013-01-01",
                end_date="2017-06-01",
                tenure=4.4
            )
        ],
        skills=[],
        match_reasoning="Perfect role and location match, has HSBC alumni experience, but currently works at Handelsbanken and alumni=False"
    )
    
    # Profile 3: Valid candidate - HSBC UK (should pass all filters)
    sarah_people_data = PeopleData(
        forename="Sarah",
        surname="Johnson",
        gender="Female",
        country="United Kingdom",  # Correct location
        city="London",
        latest_role="Chief Risk Officer",
        company_name="HSBC",  # Correct company
        tenure=6.0,
        career_history="Jan-2018 | Present: Chief Risk Officer at HSBC, London\nMar-2014 | Jan-2018: Deputy Chief Risk Officer at HSBC, London",
        skills="Risk Management, Banking, Financial Services, Regulatory Compliance",
        linkedinURL="https://linkedin.com/in/sarah-johnson-hsbc-cro"
    )
    
    sarah_pipeline_data = PipelineData(
        first_name="Sarah",
        last_name="Johnson",
        gender="Female",
        latest_role="Chief Risk Officer",
        company_name="HSBC",
        country="United Kingdom",
        city="London",
        tenure=6,
        role_match=1.0,  # Perfect role match
        skills_match=0.95,
        location_match=1.0,  # Correct location
        gender_match=1.0,
        tenure_match=1.0,
        education_match=0.0,
        total_score=4.95,
        linkedinURL="https://www.linkedin.com/in/sarah-johnson-hsbc-cro"
    )
    
    sarah_profile = CandidateProfile(
        people_data=sarah_people_data,
        pipeline_data=sarah_pipeline_data,
        career_history=[
            CareerHistory(
                people_id=0,
                role="Chief Risk Officer",
                company_name="HSBC",
                start_date="2018-01-01",
                end_date="Present",
                tenure=6.0
            ),
            CareerHistory(
                people_id=0,
                role="Deputy Chief Risk Officer",
                company_name="HSBC",
                start_date="2014-03-01",
                end_date="2018-01-01",
                tenure=3.8
            )
        ],
        skills=[],
        match_reasoning="Perfect match - current HSBC CRO in UK with sufficient tenure"
    )
    
    return [robert_profile, william_profile, sarah_profile]

def test_final_validation():
    """Test the final validation filtering logic"""
    logger.info("=" * 60)
    logger.info("TESTING ENHANCED EXTERNAL SEARCH FILTERING LOGIC")
    logger.info("=" * 60)

    # Test 1: Alumni = FALSE (current employees only)
    logger.info("TEST 1: Alumni = FALSE (current employees only)")
    logger.info("-" * 40)

    plan_data = create_hsbc_cro_plan()
    test_profiles = create_test_profiles()

    logger.info(f"Plan Configuration:")
    logger.info(f"  - Target Company: {plan_data['companies']}")
    logger.info(f"  - Target Location: {plan_data['country']}")
    logger.info(f"  - Alumni Setting: {plan_data['include_alumni']} (current employees only)")
    logger.info(f"  - Minimum Tenure: {plan_data['minimum_tenure']} years")
    logger.info("")

    # Create search engine instance
    search_engine = ExternalSearchEngine()

    # Test final validation
    logger.info(f"Testing {len(test_profiles)} candidate profiles:")
    logger.info("")

    validated_profiles = search_engine._final_validation_filter(test_profiles, plan_data)

    logger.info("")
    logger.info("VALIDATION RESULTS SUMMARY - Alumni = FALSE")
    logger.info("-" * 40)

    for i, profile in enumerate(test_profiles):
        name = f"{profile.people_data.forename} {profile.people_data.surname}"
        company = profile.pipeline_data.company_name
        location = profile.people_data.country
        passed = profile in validated_profiles

        logger.info(f"{i+1}. {name}")
        logger.info(f"   Company: {company}")
        logger.info(f"   Location: {location}")
        logger.info(f"   Result: {'✅ PASSED' if passed else '❌ FILTERED'}")

        if not passed:
            # Explain why it was filtered
            if company != "HSBC":
                logger.info(f"   Reason: Wrong company (expected HSBC, got {company})")
            elif location != "United Kingdom":
                logger.info(f"   Reason: Wrong location (expected United Kingdom, got {location})")
            else:
                logger.info(f"   Reason: Other validation failure")
        logger.info("")

    # Expected results for alumni=FALSE
    expected_passed_no_alumni = 1  # Only Sarah Johnson should pass
    actual_passed_no_alumni = len(validated_profiles)

    logger.info(f"Expected profiles to pass: {expected_passed_no_alumni}")
    logger.info(f"Actual profiles passed: {actual_passed_no_alumni}")

    test1_passed = actual_passed_no_alumni == expected_passed_no_alumni
    if test1_passed:
        logger.info("✅ TEST 1 PASSED: Alumni=FALSE filtering working correctly!")
    else:
        logger.error("❌ TEST 1 FAILED: Alumni=FALSE filtering needs adjustment!")

    logger.info("")
    logger.info("=" * 60)

    # Test 2: Alumni = TRUE (current employees + alumni)
    logger.info("TEST 2: Alumni = TRUE (current employees + alumni)")
    logger.info("-" * 40)

    plan_data_alumni = create_hsbc_cro_plan()
    plan_data_alumni['include_alumni'] = True  # Enable alumni

    logger.info(f"Plan Configuration:")
    logger.info(f"  - Target Company: {plan_data_alumni['companies']}")
    logger.info(f"  - Target Location: {plan_data_alumni['country']}")
    logger.info(f"  - Alumni Setting: {plan_data_alumni['include_alumni']} (current employees + alumni)")
    logger.info(f"  - Minimum Tenure: {plan_data_alumni['minimum_tenure']} years")
    logger.info("")

    validated_profiles_alumni = search_engine._final_validation_filter(test_profiles, plan_data_alumni)

    logger.info("")
    logger.info("VALIDATION RESULTS SUMMARY - Alumni = TRUE")
    logger.info("-" * 40)

    for i, profile in enumerate(test_profiles):
        name = f"{profile.people_data.forename} {profile.people_data.surname}"
        company = profile.pipeline_data.company_name
        location = profile.people_data.country
        passed = profile in validated_profiles_alumni

        logger.info(f"{i+1}. {name}")
        logger.info(f"   Company: {company}")
        logger.info(f"   Location: {location}")
        logger.info(f"   Result: {'✅ PASSED' if passed else '❌ FILTERED'}")

        if not passed:
            # Explain why it was filtered
            if location != "United Kingdom":
                logger.info(f"   Reason: Wrong location (expected United Kingdom, got {location})")
            else:
                logger.info(f"   Reason: Other validation failure")
        elif name == "William O'Carroll":
            logger.info(f"   Reason: HSBC alumni with current CRO role in UK")
        logger.info("")

    # Expected results for alumni=TRUE
    expected_passed_alumni = 2  # Sarah Johnson + William O'Carroll should pass (both in UK, William has HSBC alumni)
    actual_passed_alumni = len(validated_profiles_alumni)

    logger.info(f"Expected profiles to pass: {expected_passed_alumni}")
    logger.info(f"Actual profiles passed: {actual_passed_alumni}")

    test2_passed = actual_passed_alumni == expected_passed_alumni
    if test2_passed:
        logger.info("✅ TEST 2 PASSED: Alumni=TRUE filtering working correctly!")
    else:
        logger.error("❌ TEST 2 FAILED: Alumni=TRUE filtering needs adjustment!")

    logger.info("")
    logger.info("=" * 60)
    logger.info("OVERALL TEST RESULTS")
    logger.info("=" * 60)

    overall_passed = test1_passed and test2_passed
    if overall_passed:
        logger.info("✅ ALL TESTS PASSED: Enhanced filtering logic working correctly!")
    else:
        logger.error("❌ SOME TESTS FAILED: Filtering logic needs adjustment!")

    return overall_passed

if __name__ == "__main__":
    success = test_final_validation()
    sys.exit(0 if success else 1)
